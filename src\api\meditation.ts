import { http } from "@/utils/http";
import { baseUrlApi } from "./utils";

// 冥想内容数据类型定义
export type MeditationContent = {
  id: string;
  type: "meditation" | "sleep" | "nature";
  sub_type: "course" | "single" | "series";
  parent_id?: string;
  title: string;
  description: string;
  cover_url?: string;
  audio_url?: string;
  video_url?: string;
  duration: number;
  tags_text: string;
  favorite_count: number;
  status: "published" | "unpublished";
  is_recommended?: boolean;
  created_at: string;
  updated_at?: string;
  parent?: MeditationContent;
  tags: MeditationTag[];
};

// 冥想标签数据类型定义
export type MeditationTag = {
  id: string;
  name: string;
  created_at: string;
  usage_count?: number;
};

// 冥想内容列表响应类型
export type MeditationContentListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: MeditationContent[];
  };
};

// 冥想内容详情响应类型
export type MeditationContentResponse = {
  code: number;
  message?: string;
  data: MeditationContent;
};

// 冥想标签列表响应类型
export type MeditationTagListResponse = {
  code: number;
  message?: string;
  data: {
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    items: MeditationTag[];
  };
};

// 冥想标签响应类型
export type MeditationTagResponse = {
  code: number;
  message?: string;
  data: MeditationTag;
};

// 通用删除响应类型
export type DeleteResponse = {
  code: number;
  message?: string;
};

// 冥想内容列表查询参数
export type MeditationContentListParams = {
  page?: number;
  limit?: number;
  search?: string;
  type?: "meditation" | "sleep" | "nature";
  sub_type?: "course" | "single" | "series";
  status?: "published" | "unpublished";
  is_recommended?: boolean;
};

// 冥想内容创建参数
export type CreateMeditationContentParams = {
  title: string;
  description: string;
  type: "meditation" | "sleep" | "nature";
  sub_type: "course" | "single" | "series";
  parent_id?: string;
  cover_url?: string;
  audio_url?: string;
  video_url?: string;
  duration: number;
  tag_ids: number[];
  status: "published" | "unpublished";
};

// 冥想内容更新参数
export type UpdateMeditationContentParams = {
  title?: string;
  description?: string;
  type?: "meditation" | "sleep" | "nature";
  sub_type?: "course" | "single" | "series";
  parent_id?: string;
  cover_url?: string;
  audio_url?: string;
  video_url?: string;
  duration?: number;
  tag_ids?: number[];
  status?: "published" | "unpublished";
};

// 冥想标签列表查询参数
export type MeditationTagListParams = {
  page?: number;
  limit?: number;
  search?: string;
};

// 冥想标签创建参数
export type CreateMeditationTagParams = {
  name: string;
};

// 冥想标签更新参数
export type UpdateMeditationTagParams = {
  name: string;
};

// 批量删除标签参数
export type BatchDeleteTagsParams = {
  tag_ids: number[];
};

// 课程章节数据类型定义
export type CourseChapter = {
  id: string;
  type: "meditation" | "sleep" | "nature";
  sub_type: "single";
  parent_id: string;
  title: string;
  description: string;
  cover_url?: string;
  audio_url?: string;
  video_url?: string;
  duration: number;
  tags_text: string;
  favorite_count: number;
  status: "draft" | "published" | "archived";
  is_recommended: boolean;
  sort_order: number;
  created_at: string;
  updated_at?: string;
  tags: MeditationTag[];
};

// 课程章节列表响应类型
export type CourseChapterListResponse = {
  code: number;
  message?: string;
  data: {
    course: {
      id: string;
      title: string;
      description: string;
    };
    chapters: CourseChapter[];
  };
};

// 推荐状态更新参数
export type UpdateRecommendParams = {
  is_recommended: boolean;
};

// 状态更新参数
export type UpdateStatusParams = {
  status: "published" | "unpublished";
};

// 章节创建参数
export type CreateChapterParams = {
  title: string;
  description: string;
  cover_url?: string;
  audio_url?: string;
  video_url?: string;
  duration: number;
  tag_ids: number[];
  sort_order: number;
};

// 章节顺序调整参数
export type ReorderChaptersParams = {
  chapter_orders: {
    id: number;
    sort_order: number;
  }[];
};

// ==================== 冥想内容管理接口 ====================

/** 获取冥想内容列表（管理端） */
export const getMeditationContentList = (
  params?: MeditationContentListParams
) => {
  return http.request<MeditationContentListResponse>(
    "get",
    baseUrlApi("admin/meditation/contents"),
    {
      params
    }
  );
};

/** 创建冥想内容 */
export const createMeditationContent = (
  data: CreateMeditationContentParams
) => {
  return http.request<MeditationContentResponse>(
    "post",
    baseUrlApi("admin/meditation/contents"),
    {
      data
    }
  );
};

/** 获取冥想内容详情（管理端） */
export const getMeditationContentDetail = (id: string) => {
  return http.request<MeditationContentResponse>(
    "get",
    baseUrlApi(`admin/meditation/contents/${id}`)
  );
};

/** 更新冥想内容 */
export const updateMeditationContent = (
  id: string,
  data: UpdateMeditationContentParams
) => {
  return http.request<MeditationContentResponse>(
    "put",
    baseUrlApi(`admin/meditation/contents/${id}`),
    {
      data
    }
  );
};

/** 删除冥想内容 */
export const deleteMeditationContent = (id: string) => {
  return http.request<DeleteResponse>(
    "delete",
    baseUrlApi(`admin/meditation/contents/${id}`)
  );
};

// ==================== 冥想标签管理接口 ====================

/** 获取标签列表（管理端） */
export const getMeditationTagList = (params?: MeditationTagListParams) => {
  return http.request<MeditationTagListResponse>(
    "get",
    baseUrlApi("admin/meditation/tags"),
    {
      params
    }
  );
};

/** 创建标签 */
export const createMeditationTag = (data: CreateMeditationTagParams) => {
  return http.request<MeditationTagResponse>(
    "post",
    baseUrlApi("admin/meditation/tags"),
    {
      data
    }
  );
};

/** 更新标签 */
export const updateMeditationTag = (
  id: string,
  data: UpdateMeditationTagParams
) => {
  return http.request<MeditationTagResponse>(
    "put",
    baseUrlApi(`admin/meditation/tags/${id}`),
    {
      data
    }
  );
};

/** 删除标签 */
export const deleteMeditationTag = (id: string) => {
  return http.request<DeleteResponse>(
    "delete",
    baseUrlApi(`admin/meditation/tags/${id}`)
  );
};

/** 批量删除标签 */
export const batchDeleteMeditationTags = (data: BatchDeleteTagsParams) => {
  return http.request<DeleteResponse>(
    "delete",
    baseUrlApi("admin/meditation/tags/batch-delete"),
    {
      data
    }
  );
};

// ==================== 课程管理接口 ====================

/** 推荐/取消推荐课程 */
export const updateCourseRecommend = (
  id: string,
  data: UpdateRecommendParams
) => {
  return http.request<MeditationContentResponse>(
    "put",
    baseUrlApi(`admin/meditation/contents/${id}/recommend`),
    {
      data
    }
  );
};

/** 更新课程状态（上架/下架/重新发布） */
export const updateCourseStatus = (id: string, data: UpdateStatusParams) => {
  return http.request<MeditationContentResponse>(
    "put",
    baseUrlApi(`admin/meditation/contents/${id}/status`),
    {
      data
    }
  );
};

/** 获取课程章节列表 */
export const getCourseChapters = (courseId: string) => {
  return http.request<CourseChapterListResponse>(
    "get",
    baseUrlApi(`admin/meditation/courses/${courseId}/chapters`)
  );
};

/** 为课程添加章节 */
export const createCourseChapter = (
  courseId: string,
  data: CreateChapterParams
) => {
  return http.request<MeditationContentResponse>(
    "post",
    baseUrlApi(`admin/meditation/courses/${courseId}/chapters`),
    {
      data
    }
  );
};

/** 调整章节顺序 */
export const reorderCourseChapters = (
  courseId: string,
  data: ReorderChaptersParams
) => {
  return http.request<DeleteResponse>(
    "put",
    baseUrlApi(`admin/meditation/courses/${courseId}/chapters/reorder`),
    {
      data
    }
  );
};

/** 删除课程章节 */
export const deleteCourseChapter = (courseId: string, chapterId: string) => {
  return http.request<DeleteResponse>(
    "delete",
    baseUrlApi(`admin/meditation/courses/${courseId}/chapters/${chapterId}`)
  );
};

// ==================== 内容上架下架接口 ====================

/** 上架冥想内容 */
export const publishMeditationContent = (id: string) => {
  return http.request<MeditationContentResponse>(
    "put",
    baseUrlApi(`admin/meditation/contents/${id}/publish`)
  );
};

/** 下架冥想内容 */
export const unpublishMeditationContent = (id: string) => {
  return http.request<MeditationContentResponse>(
    "put",
    baseUrlApi(`admin/meditation/contents/${id}/unpublish`)
  );
};
